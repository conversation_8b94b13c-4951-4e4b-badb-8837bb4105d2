<template>
  <div class="readonly-audio-waveform-container" ref="containerRef">
    <canvas
      ref="canvasRef"
      class="readonly-audio-waveform-canvas"
      :width="canvasWidth"
      :height="canvasHeight"
    />
    <div v-if="!hasAudioData && showAudioWaveform" class="no-audio-message">
      {{ t('editor.audio.noAudioData') }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from "vue";
import { useReadOnlyAudioWaveform } from "./composables/useReadOnlyAudioWaveform";
import { useWaveformCoordinate } from "./composables/useWaveformCoordinate";
import { WAVEFORM_SAFE_OFFSET } from "./utils/drawing-helpers";
import { useI18n } from "@/composables/useI18n";
import { logger, LogModule } from "@/utils/logger/logger";

const props = withDefaults(defineProps<{
  fileUuid: string; // 必需：文件UUID，用于获取音频数据
  availableWidth: number; // 可用宽度
  audioDuration?: number | null; // 音频时长（ms）
  showAudioWaveform?: boolean; // 是否显示音频波形
  totalEffectDuration?: number; // 总效果时长
  virtualScrollOffset?: number; // 虚拟滚动偏移
  zoomLevel?: number; // 缩放级别
}>(), {
  showAudioWaveform: false,
  totalEffectDuration: 5000,
  virtualScrollOffset: 0,
  zoomLevel: 1.0
});

const { t } = useI18n();

// 模板引用
const containerRef = ref<HTMLDivElement | null>(null);
const canvasRef = ref<HTMLCanvasElement | null>(null);

// Canvas 尺寸
const canvasWidth = computed(() => Math.max(props.availableWidth - 20, 100));
const canvasHeight = ref(120); // 固定高度，专门用于显示音频波形

// Canvas 上下文
const canvasCtx = ref<CanvasRenderingContext2D | null>(null);

// 虚拟滚动偏移
const virtualScrollOffset = ref(props.virtualScrollOffset);
const zoomLevel = ref(props.zoomLevel);

// 监听 props 变化
watch(() => props.virtualScrollOffset, (newOffset) => {
  virtualScrollOffset.value = newOffset;
});

watch(() => props.zoomLevel, (newZoom) => {
  zoomLevel.value = newZoom;
});

// 画布区域计算
const PADDING = {
  top: 10,
  right: 10,
  bottom: 10,
  left: 10,
};

const getGraphAreaWidth = () => canvasWidth.value - PADDING.left - PADDING.right;
const getGraphAreaHeight = () => canvasHeight.value - PADDING.top - PADDING.bottom;
const getLogicalGraphAreaWidth = () => getGraphAreaWidth(); // 简化版本，不考虑复杂的逻辑宽度计算

// 坐标转换配置
const coordinateConfig = {
  padding: PADDING,
  safeOffset: WAVEFORM_SAFE_OFFSET,
};

// 模拟画布状态（简化版本）
const canvasState = {
  canvasWidth: { value: canvasWidth.value },
  canvasHeight: { value: canvasHeight.value },
  virtualScrollOffset: virtualScrollOffset,
  getEffectiveDuration: () => props.totalEffectDuration || 5000,
  getGraphAreaWidth,
  getLogicalGraphAreaWidth,
  getGraphAreaHeight,
};

// 使用坐标转换 composable
const { mapIntensityToYLocal } = useWaveformCoordinate(
  canvasState,
  coordinateConfig,
  zoomLevel
);

// 音频波形配置
const audioWaveformConfig = {
  mapIntensityToYLocal,
  getGraphAreaWidth,
  getGraphAreaHeight,
  getLogicalGraphAreaWidth,
  virtualScrollOffset,
  currentZoomLevel: zoomLevel,
  enableSilenceFiltering: true,
  silenceThresholdPercent: 2,
  showWaveformBorder: false,
  amplitudeScale: 0.75,
  amplitudeBoost: 1.5,
};

// 使用只读音频波形 composable
const { hasAudioData, drawAudioWaveform, clearCache } = useReadOnlyAudioWaveform(
  audioWaveformConfig,
  props.fileUuid
);

/**
 * 初始化 Canvas
 */
const initCanvas = () => {
  if (!canvasRef.value) return;

  const ctx = canvasRef.value.getContext('2d');
  if (!ctx) {
    logger.error(LogModule.WAVEFORM, "ReadOnlyAudioWaveform: 无法获取 Canvas 上下文");
    return;
  }

  canvasCtx.value = ctx;
  
  // 设置高DPI支持
  const devicePixelRatio = window.devicePixelRatio || 1;
  const canvas = canvasRef.value;
  
  canvas.width = canvasWidth.value * devicePixelRatio;
  canvas.height = canvasHeight.value * devicePixelRatio;
  canvas.style.width = canvasWidth.value + 'px';
  canvas.style.height = canvasHeight.value + 'px';
  
  ctx.scale(devicePixelRatio, devicePixelRatio);
  
  logger.debug(LogModule.WAVEFORM, "ReadOnlyAudioWaveform: Canvas 初始化完成", {
    width: canvasWidth.value,
    height: canvasHeight.value,
    devicePixelRatio
  });
};

/**
 * 绘制波形
 */
const drawWaveform = () => {
  if (!canvasCtx.value || !props.showAudioWaveform) return;

  const ctx = canvasCtx.value;
  
  // 清空画布
  ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value);

  // 绘制背景
  ctx.fillStyle = "#1a1a1a";
  ctx.fillRect(0, 0, canvasWidth.value, canvasHeight.value);

  // 绘制简单的网格线（可选）
  drawSimpleGrid(ctx);

  // 绘制音频波形
  if (hasAudioData.value) {
    drawAudioWaveform(ctx);
  }
};

/**
 * 绘制简单网格
 */
const drawSimpleGrid = (ctx: CanvasRenderingContext2D) => {
  ctx.save();
  ctx.strokeStyle = "rgba(255, 255, 255, 0.1)";
  ctx.lineWidth = 0.5;

  // 绘制水平中线（零基线）
  const centerY = mapIntensityToYLocal(0);
  ctx.beginPath();
  ctx.moveTo(PADDING.left, centerY);
  ctx.lineTo(canvasWidth.value - PADDING.right, centerY);
  ctx.stroke();

  ctx.restore();
};

/**
 * 处理 Canvas 尺寸变化
 */
const handleResize = () => {
  nextTick(() => {
    initCanvas();
    clearCache(); // 清理缓存，强制重新绘制
    drawWaveform();
  });
};

// 监听相关属性变化，重新绘制
watch([
  () => props.showAudioWaveform,
  () => props.availableWidth,
  () => props.virtualScrollOffset,
  () => props.zoomLevel,
  hasAudioData
], () => {
  nextTick(() => {
    drawWaveform();
  });
});

// 监听画布宽度变化
watch(canvasWidth, () => {
  handleResize();
});

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initCanvas();
    drawWaveform();
  });
});

// 组件卸载
onBeforeUnmount(() => {
  clearCache();
});
</script>

<style scoped>
.readonly-audio-waveform-container {
  position: relative;
  width: 100%;
  height: 120px;
  background: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.readonly-audio-waveform-canvas {
  display: block;
  width: 100%;
  height: 100%;
  cursor: default; /* 只读，不显示交互光标 */
}

.no-audio-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
  pointer-events: none;
}
</style>
